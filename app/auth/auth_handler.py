import os
import jwt
from jwt import InvalidTokenError
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>, status
from typing import Optional

def decodeJWT(token: str) -> Optional[dict]:
  try:
    jwt_secret_key = os.getenv('JWT_SECRET_KEY')
    
    if jwt_secret_key is None:
      raise ValueError('JWT Secret Key could not be found in the environment.')
    
    decoded_token = jwt.decode(token, jwt_secret_key, algorithms=["HS256"])

    return decoded_token
  except InvalidTokenError:
    raise HTTPException(
      status_code=status.HTTP_401_UNAUTHORIZED,
      detail="Invalid token",
      headers={"WWW-Authenticate": "Bearer"},
    )
  except Exception as e:
    raise HTTPException(
      status_code=status.HTTP_401_UNAUTHORIZED,
      detail=f"An error occurred: {str(e)}",
      headers={"WWW-Authenticate": "Bearer"},
    )