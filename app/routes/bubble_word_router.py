import logging
from typing import Any

from fastapi import Depends, APIRouter, HTTPException
from app.handlers.bubble_word_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel, Field
from app.models.bubble_word_request import B<PERSON>ble<PERSON>ordRequest
from app.auth.dependencies import get_current_user
from typing import List, Optional

import json

router = APIRouter()
logger = logging.getLogger(__name__)

# start_date and end_date are required fields for BubbleWordRequest
class BubbleWordRequest(BaseModel):
    start_date: str = Field(..., description="Start date in the format YYYY-MM-DD")
    end_date: str = Field(..., description="End date in the format YYYY-MM-DD")
    # The other fields are optional
    retrieve_stopwords: Optional[bool] = True
    algorithm: Optional[str] = "bag_of_words"
    top_terms_count: Optional[int] = 0

class IdLeadListRequest(BaseModel):
    id_lead_list: List[int]

@router.post("/bubble-word")
async def get_bubble_word(bubbleWordRequest: BubbleWordRequest, current_user = Depends(get_current_user)):

    # Call chatbot response
    handler = BubbleWordHandler()
    response = await handler.get_bubble_word(
        current_user['id_platform'],
        current_user['id_user'],
        bubbleWordRequest
    )

    logger.info("Generate response=%s",  response)
    return response

@router.post("/bubble-word/id-lead-filter")
async def get_bubble_word_by_id_lead_list(idMessageListRequest: IdLeadListRequest, current_user = Depends(get_current_user)):

    handler = BubbleWordHandler()
    response = await handler.get_bubble_word_by_id_lead_list(
        current_user['id_platform'],
        current_user['id_user'],
        idMessageListRequest
    )

    logger.info("Generate response=%s",  response)
    return response

