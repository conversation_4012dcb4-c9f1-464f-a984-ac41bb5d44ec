import logging
import pymysql
import os

class RDSConnector:
    _instance = None

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.host = os.environ['RDS_DB_HOST']
        self.port = os.environ['RDS_DB_PORT_NUMBER']
        self.db_name = os.environ['RDS_DB_NAME']
        self.user = os.environ['RDS_DB_USERNAME']
        self.password = os.environ['RDS_DB_PASSWORD']

    def get_connection(self):
        try:
            self.con = pymysql.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                db=self.db_name,
                cursorclass=pymysql.cursors.DictCursor
            )

            return self.con
        except Exception as e:
            self.logger.error(f"Error connecting to RDS database: {e}")
            raise e

    def __disconnect__(self):
        self.con.close()
