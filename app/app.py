import json
import uuid
import uvicorn

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from mangum import Mangum

from app.routes import bubble_word_router
from app.core.monitoring import logging_config
from app.core.middlewares.correlation_id_middleware import CorrelationIdMiddleware
from app.core.middlewares.logging_middleware import LoggingMiddleware
from app.core.exception.exception_handler import exception_handler
from app.core.exception.http_exception_handler import http_exception_handler
from fastapi.middleware.cors import CORSMiddleware

###############################################################################
#   Application object                                                        #
###############################################################################

app = FastAPI(
    title="Semantic Word Cloud",
    description="Semantic Word Cloud",
    version="1.0",
    docs_url='/semantic-word-cloud/docs',
    openapi_url='/semantic-word-cloud/openapi.json',
    redoc_url=None
)

###############################################################################
#   Logging configuration                                                     #
###############################################################################

logging_config.configure_logging(
    level='DEBUG', service='semantic-word-cloud', instance=str(uuid.uuid4()))

###############################################################################
#   Error handlers configuration                                              #
###############################################################################

app.add_exception_handler(Exception, exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)

###############################################################################
#   Middlewares configuration                                                 #
###############################################################################

# Tip : middleware order : CorrelationIdMiddleware > LoggingMiddleware -> reverse order
app.add_middleware(LoggingMiddleware)
app.add_middleware(CorrelationIdMiddleware)
origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

###############################################################################
#   Routers configuration                                                     #
###############################################################################

app.include_router(bubble_word_router.router, prefix='/semantic-word-cloud')


@app.get("/semantic-word-cloud/health")
def main():
    msg = "Api is up and running!!"
    return {"message": msg}

###############################################################################
#   Handler for AWS Lambda                                                    #
###############################################################################


handler = Mangum(app)

###############################################################################
#   Run the self contained application                                        #
###############################################################################

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=5000)
