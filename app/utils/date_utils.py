from datetime import datetime, timedelta

class DateUtils:
  @staticmethod
  def process_date_range_for_db(start_date, end_date):
    start_date = datetime.strptime(start_date, '%Y-%m-%d').strftime('%Y-%m-%d 00:00:00')
    end_date = datetime.strptime(end_date, '%Y-%m-%d').strftime('%Y-%m-%d 23:59:59')
    return start_date, end_date

  @staticmethod
  def calculate_previous_period(start_date, end_date):
    """
    Calculate the previous period of equal length immediately before the given period.
    
    Args:
        start_date (str): Start date in format 'YYYY-MM-DD'
        end_date (str): End date in format 'YYYY-MM-DD'
        
    Returns:
        tuple: (previous_start_date, previous_end_date) in format 'YYYY-MM-DD'
    """
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d')
    
    # Calculate the duration of the requested period
    delta = end_dt - start_dt
    
    # Calculate the previous period
    previous_end_dt = start_dt - timedelta(days=1)
    previous_start_dt = previous_end_dt - delta
    
    # Format as strings
    previous_start_date = previous_start_dt.strftime('%Y-%m-%d')
    previous_end_date = previous_end_dt.strftime('%Y-%m-%d')
    
    return previous_start_date, previous_end_date