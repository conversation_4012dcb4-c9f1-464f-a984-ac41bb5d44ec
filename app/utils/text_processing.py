import re
from typing import List, Optional
from sklearn.feature_extraction.text import CountVectorizer
import pandas
import logging

class TextProcessing:
    @staticmethod
    def convert_to_lowercase(text: str) -> str:
        return text.lower()

    @staticmethod
    def remove_urls(text: str) -> str:
        url_pattern = re.compile(r'\b(?:https?://)?\w[\w.-]+\.\w+\b')
        return url_pattern.sub(r'', text)

    @staticmethod
    def remove_emails(text: str) -> str:
        email_pattern = re.compile(r'\S+@\S+')
        return email_pattern.sub(r'', text)
    
    @staticmethod
    def replace_line_breaks(text: str) -> str:
        return text.replace('\n', ' ').replace('\r', ' ')
    
    @staticmethod
    def remove_numbers(text: str) -> str:
        return ''.join(filter(lambda x: not x.isdigit(), text))
    
    @staticmethod
    def replace_commas(text: str) -> str:
        return text.replace(',', ' ')

    @staticmethod
    def replace_special_chars(text: str) -> str:
        # Disconsider portuguese accented chars and hyphens (because of compound words)
        pattern = re.compile(r'[^a-zA-Z0-9\s\-áéíóúãõâêôç]')

        # Remove hyphens that are adjacent to spaces (either before or after)
        text = re.sub(r'(?<=\s)-|-(?=\s)', ' ', text)

        return pattern.sub('', text)
    
    @staticmethod
    def remove_short_words(text: str, length: int = 3) -> str:
        words = text.split()
        filtered_words = [word for word in words if len(word) > length]
        return ' '.join(filtered_words)

    @staticmethod
    def lemmatize(text: str) -> str:
        """
        Transforms words in the input text to their base or dictionary form (lemma) using a lemmatizer.
        Lemmatization is a more sophisticated process compared to stemming. Instead of merely reducing words,
        it considers a language's full vocabulary to apply a morphological analysis, aiming to return the base form of a word.
        For instance, 'correndo' and 'correu' should both be lemmatized to 'correr'.
        
        Note: As of the last update, there's no comprehensive out-of-the-box lemmatizer for Portuguese in popular libraries like NLTK or SpaCy.
        """
        # TODO: Implement the lemmatization logic here
        return text
    
    @staticmethod
    def process_messages(messages_list):
        try:
            for item in messages_list:
                text = item['body']
                
                text = TextProcessing.convert_to_lowercase(text)
                text = TextProcessing.remove_urls(text)
                text = TextProcessing.remove_emails(text)
                text = TextProcessing.replace_line_breaks(text)
                text = TextProcessing.remove_numbers(text)
                text = TextProcessing.replace_commas(text)
                text = TextProcessing.replace_special_chars(text)
                text = TextProcessing.remove_short_words(text)

                item['body'] = text
                
            return messages_list
        except Exception as e:
            logging.error(f"An error occurred in TextProcessing.process_messages: {e}")
            raise e
    
    @staticmethod
    def transform_messages_to_bag_of_words_dictionary(body_list, stopwords: Optional[List[str]] = None):
        try:
            if not body_list:
                return {}
            
            dataframe = pandas.DataFrame(body_list, columns=["body"])
            stringified_dataframe = ' '.join(dataframe['body']) 
            
            # Transform stringified_dataframe into a bag-of-words matrix
            vectorizer = CountVectorizer(stop_words=stopwords)
            bag_of_words_matrix = vectorizer.fit_transform([stringified_dataframe])

            # Convert the matrix into a dense array
            dense_bag_of_words = bag_of_words_matrix.toarray()

            # Transform the array into a dictionary
            bag_of_words_dict = dict(zip(vectorizer.get_feature_names_out(), dense_bag_of_words[0]))

            # Sort the dictionary by values in descending order
            sorted_bag_of_words_dict = dict(sorted(bag_of_words_dict.items(), key=lambda item: item[1], reverse=True))
            
            return sorted_bag_of_words_dict
        except Exception as e:
            logging.error(f"An error occurred in TextProcessing.transform_messages_to_bag_of_words_dictionary: {e}")
            raise e
    
    @staticmethod
    def transform_word_cloud_response(dataframe_dictionary, algorithm, top_terms_count, monitored_words):
        try:
            # Sort the dictionary by its values
            sorted_terms = sorted(dataframe_dictionary.items(), key=lambda x: x[1], reverse=True)

            # Terms will be full dataframe if top_terms_count is falsy
            if not top_terms_count:
                top_terms_count = len(dataframe_dictionary)
            
            # Retrieve the top terms
            top_terms = sorted_terms[:top_terms_count]
            
            # Calculate the total frequencies
            total_frequency = sum(dataframe_dictionary.values())
            top_terms_frequency = sum([freq for _, freq in top_terms])
            
            # Generate the list for top terms
            terms_retrieved_list = []
            for term, freq in top_terms:
                terms_retrieved_list.append({
                    "term": term,
                    "algorithm_frequency": int(freq),
                    "collection_algorithm_percentage": round(float(freq) / total_frequency, 5),
                    "terms_algorithm_percentage": round(float(freq) / top_terms_frequency, 5),
                })

            # Construct the final response
            response = {
                "algorithm": algorithm,
                "collection_terms_retrieved": int(len(dataframe_dictionary)),
                "collection_algorithm_frequency": int(total_frequency),
                "terms_retrieved": int(len(top_terms)),
                "terms_algorithm_frequency": int(top_terms_frequency),
                "terms_retrieved_list": terms_retrieved_list
            }

            return response
        except Exception as e:
            logging.error(f"An error occurred in TextProcessing.transform_word_cloud_response: {e}")
            raise e
            
    @staticmethod
    def transform_comparative_word_cloud_response(current_data, previous_data, algorithm, top_terms_count):
        """
        Create a response with both current and previous period word cloud data.
        
        Args:
            current_data (dict): Dictionary of terms and frequencies for current period
            previous_data (dict): Dictionary of terms and frequencies for previous period
            algorithm (str): The algorithm used for analysis
            top_terms_count (int): Number of top terms to include
            
        Returns:
            dict: Response with current and previous period data
        """
        try:
            # Get the word cloud responses for both periods
            current_response = TextProcessing.transform_word_cloud_response(
                current_data, algorithm, top_terms_count, []
            )
            
            previous_response = TextProcessing.transform_word_cloud_response(
                previous_data, algorithm, top_terms_count, []
            )
            
            # Construct the comparative response
            comparative_response = {
                "current": {
                    "terms_retrieved_list": current_response["terms_retrieved_list"]
                },
                "previous": {
                    "terms_retrieved_list": previous_response["terms_retrieved_list"]
                }
            }
            
            return comparative_response
        except Exception as e:
            logging.error(f"An error occurred in TextProcessing.transform_comparative_word_cloud_response: {e}")
            raise e
