# sentiments_utils.py

class SentimentsUtils:
    @staticmethod
    def has_valid_sentiment(sentiments_array):
        available_sentiments = ['positive', 'neutral', 'negative']
        
        if (not sentiments_array):
            return False
        else:
            has_positive, has_neutral, has_negative = [sentiment in sentiments_array for sentiment in available_sentiments]
                
            has_valid_sentiment = has_positive or has_neutral or has_negative
            
            return has_valid_sentiment

    @staticmethod
    def process_sentiments_where(sentiments_array, is_first_where_condition=False):
        str_ = ''
        len_ = len(sentiments_array) if sentiments_array else 0
        has_valid_sentiment = SentimentsUtils.has_valid_sentiment(sentiments_array)

        if len_ == 0 or not has_valid_sentiment:
            return str_

        if sentiments_array and len(sentiments_array):
            for i in range(len_):
                if i == 0:
                    str_ += ' '
                    if is_first_where_condition:
                        str_ += '('
                    else:
                        str_ += 'AND ('
                else:
                    str_ += 'OR '

                sentiment = sentiments_array[i]
                if sentiment == 'positive':
                    str_ += '(positive_count >= neutral_count AND positive_count >= negative_count) '
                elif sentiment == 'neutral':
                    str_ += '(neutral_count >= positive_count AND neutral_count >= negative_count) '
                elif sentiment == 'negative':
                    str_ += '(negative_count >= positive_count AND negative_count >= neutral_count) '

            str_ += ')'

        return str_