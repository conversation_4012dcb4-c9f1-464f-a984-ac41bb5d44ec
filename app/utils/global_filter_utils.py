import logging
from typing import List, Dict, Optional
from app.aws.connections.rds_connector import RDSConnector
from app.utils.csat_utils import CsatUtils
from app.utils.sentiments_utils import SentimentsUtils

class GlobalFilterUtils:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # create an instance of the RDSConnector class
        rds_connector = RDSConnector()
        # get a database connection
        self.conn = rds_connector.get_connection()

    # WARNING: changes in these filter query functions should be reflected in multiple microservices

    async def filter_id_lead_list(self, params):
        try:
            start_date = params['start_date']
            end_date = params['end_date']
            id_platform = params['id_platform']

            actions_array = params.get('action', None)
            csat_array = params.get('csat', None)
            sentiments_array = params.get('sentiments', None)
            status_array = params.get('status', None)

            has_actions = actions_array and len(actions_array) > 0
            has_csat = CsatUtils.has_valid_csat(csat_array) if csat_array else False
            has_sentiments = SentimentsUtils.has_valid_sentiment(sentiments_array) if sentiments_array else False
            has_status = status_array and len(status_array) > 0

            # Constructing SQL logic
            query_parts = ["SELECT cl.id_lead "]
            query_params = []

            if has_sentiments:
                query_parts.append("""
                    FROM (
                    SELECT cl.id_lead as id_lead
                    ,COUNT(CASE WHEN ams.sentiment = 'POSITIVE' THEN 1 END) AS positive_count
                    ,COUNT(CASE WHEN ams.sentiment = 'NEUTRAL' THEN 1 END) AS neutral_count
                    ,COUNT(CASE WHEN ams.sentiment = 'NEGATIVE' THEN 1 END) AS negative_count
                """)
            
            query_parts.append("FROM prod_cleversale.cs_lead cl ")

            if has_actions or has_csat or has_status:
                query_parts.append("""
                    INNER JOIN prod_cleversale.cs_lead_history clh ON cl.id_lead = clh.fk_id_lead 
                    AND clh.created_at >= %s
                """)
                query_params.append(start_date)
                
            if has_actions:
                placeholders = ', '.join(['%s'] * len(actions_array))
                query_parts.append(f"AND clh.fk_id_action IN ({placeholders}) ")
                query_params.extend(actions_array)

            if has_status:
                placeholders = ', '.join(['%s'] * len(status_array))
                query_parts.append(f"AND clh.fk_id_status IN ({placeholders}) ")
                query_params.extend(status_array)

            if has_csat:
                placeholders = ', '.join(['%s'] * len(csat_array))
                query_parts.append(f"""
                    INNER JOIN prod_cleversale.cs_attendant_evaluation cae ON cae.fk_id_lead_history = clh.id_lead_history
                    AND cae.evaluation IN ({placeholders})
                """)
                query_params.extend(csat_array)

                
            if has_sentiments:
                query_parts.append("""
                    INNER JOIN app_chat ac ON ac.fk_id_lead = cl.id_lead
                    AND ac.fk_id_platform = %s
                    AND ac.created_at >= %s
                    INNER JOIN app_message am ON am.fk_id_chat = ac.id_chat
                    AND am.date_created >= %s
                    AND am.direction = 'inbound-api'
                    INNER JOIN app_message_sentiment ams ON am.id_message = ams.fk_id_message
                    AND ams.created_at >= %s
                """)
                query_params.extend([id_platform, start_date, start_date, start_date])
                
            query_parts.append("""
                WHERE cl.fk_id_platform = %s
                AND cl.created_at BETWEEN %s AND %s
            """)
            query_params.extend([id_platform, start_date, end_date])

            if has_sentiments:
                query_parts.append(f"""
                    GROUP BY cl.id_lead
                    HAVING {SentimentsUtils.process_sentiments_where(sentiments_array, True)}
                    ) AS cl
                """)
            
            query = ' '.join(query_parts)

            with self.conn.cursor() as cursor:
                cursor.execute(query, query_params)
                result = cursor.fetchall()
                
            return result
            
        except Exception as e:
            self.logger.error("Failed to execute global_filter_utils: %s", str(e))
            raise
        finally:
            self.conn.close()
