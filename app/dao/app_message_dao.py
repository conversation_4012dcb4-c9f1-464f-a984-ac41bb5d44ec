import logging
from typing import Optional
from fastapi import HTTPException
from app.aws.connections.rds_connector import RDSConnector
import os
import asyncio
import concurrent.futures

class AppMessageDAO:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # create an instance of the RDSConnector class
        rds_connector = RDSConnector()
        # get a database connection
        self.conn = rds_connector.get_connection()

    async def get_messages(self, params):
        try:
            start_date = params['start_date']
            end_date = params['end_date']
            id_platform = params['id_platform']

            query_parts = ["SELECT acm.id_message, acm.body "]
            query_params = []

            query_parts.append("FROM audit_landzone.audit_chat_messages acm ")

            query_parts.append(
                """
                INNER JOIN audit_landzone.audit_chats ac ON acm.fk_id_chat = ac.id_chat
                    AND ac.fk_id_platform = %s
                """
            )
            query_params.append(id_platform)
            
            query_parts.append(
                """
                WHERE ac.date_chat BETWEEN %s and %s
                    AND acm.direction = 'Cliente'
                """
            )
            query_params.extend([start_date, end_date])

            sql = ' '.join(query_parts)

            with self.conn.cursor() as cursor:
                loop = asyncio.get_event_loop()
                with concurrent.futures.ThreadPoolExecutor() as pool:
                    # Execute the query with parameters
                    await loop.run_in_executor(pool, cursor.execute, sql, query_params)
                    # Fetch the results
                    result = await loop.run_in_executor(pool, cursor.fetchall)
                    # Filter out null messages and remove id_chat
                    result = [{'body': item['body']} for item in result if item['body'] is not None]

            return result
        except Exception as e:
            self.logger.error("Failed to execute select query: %s", str(e))
            raise
        finally:
            # Close the connection
            self.conn.close()

    async def get_messages_by_id_lead_list(self, id_platform, id_lead_list):
        try:
            query_params = []
            query_parts = ["""
                SELECT acm.body
                FROM audit_landzone.audit_chats ac
                INNER JOIN audit_landzone.audit_chat_messages acm ON acm.fk_id_chat = ac.id_chat
                WHERE ac.fk_id_platform = %s
                AND acm.direction = 'Cliente'
            """]

            query_params.extend([id_platform])

            placeholders = ', '.join(['%s'] * len(id_lead_list))
            query_parts.append(f"AND ac.external_id IN ({placeholders}) ")
            query_params.extend(id_lead_list)

            sql = ' '.join(query_parts)

            with self.conn.cursor() as cursor:
                loop = asyncio.get_event_loop()
                with concurrent.futures.ThreadPoolExecutor() as pool:
                    # Execute the query with parameters
                    await loop.run_in_executor(pool, cursor.execute, sql, query_params)
                    # Fetch the results
                    result = await loop.run_in_executor(pool, cursor.fetchall)
                    # Filter out null messages and remove id_chat
                    result = [{'body': item['body']} for item in result if item['body'] is not None]

            return result
        except Exception as e:
            self.logger.error("Failed to execute select query: %s", str(e))
            raise
        finally:
            self.conn.close()

    async def get_additional_stopwords(self, id_platform):
        try:
            with self.conn.cursor() as cursor:
                sql = """
                    SELECT stopword
                    FROM audit_landzone.audit_platform_stopword aps
                    WHERE aps.fk_id_platform = %s
                """
                
                loop = asyncio.get_event_loop()
                with concurrent.futures.ThreadPoolExecutor() as pool:
                    result = await loop.run_in_executor(pool, cursor.execute, sql, (id_platform,))
                    result = await loop.run_in_executor(pool, cursor.fetchall)
                
                additional_stopwords = [row["stopword"] for row in result]
            
            return additional_stopwords
        except Exception as e:
            self.logger.error("Failed to execute get_additional_stopwords: %s", str(e))
            raise
        finally:
            self.conn.close()
