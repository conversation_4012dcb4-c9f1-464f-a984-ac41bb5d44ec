import logging
from app.dao.app_message_dao import AppMessageDAO
import nltk
import asyncio
from app.utils.text_processing import TextProcessing
from app.utils.date_utils import DateUtils

class BubbleWordHandler:

    def __init__(self):
        # Initialize logger and other necessary components
        self.logger = logging.getLogger(__name__)

    def extend_db_stopwords(self, db_stopwords):
        stopwords = [word.lower() for word in db_stopwords]

        # Download stopwords only if needed, otherwise only set the stopwords
        try:
            stopwords.extend(nltk.corpus.stopwords.words('portuguese'))
        except LookupError:
            self.logger.info(
                f"Downloading nltk stopwords library"
            )
            
            nltk.download('stopwords')
            stopwords.extend(nltk.corpus.stopwords.words('portuguese'))

        # Add extra Portuguese stopwords
        with open('./app/handlers/global_stopwords.txt', 'r') as file:
            global_stopwords = [line.strip() for line in file if line.strip()]
        
        stopwords.extend(global_stopwords)

        # Removing duplicates
        stopwords = list(set(stopwords))
        
        return stopwords

    async def get_bubble_word(self, id_platform, id_user, params):
        try:
            # Process current period date range for database format
            current_start_date, current_end_date = DateUtils.process_date_range_for_db(params.start_date, params.end_date)
            
            # Calculate the previous period date range
            previous_start_date, previous_end_date = DateUtils.calculate_previous_period(params.start_date, params.end_date)
            previous_start_date, previous_end_date = DateUtils.process_date_range_for_db(previous_start_date, previous_end_date)

            # Convert params object to generic dictionary
            params_dict = vars(params)

            # Prepare parameters for current period query
            current_messages_params = {
                'id_platform': id_platform,
                'start_date': current_start_date,
                'end_date': current_end_date
            }
            
            # Prepare parameters for previous period query
            previous_messages_params = {
                'id_platform': id_platform,
                'start_date': previous_start_date,
                'end_date': previous_end_date
            }
            
            # self.dao tasks will run concurrently
            self.dao1 = AppMessageDAO()
            self.dao2 = AppMessageDAO()
            if (params.retrieve_stopwords): self.dao3 = AppMessageDAO()
            
            # Create tasks for current and previous periods
            get_current_messages_task = asyncio.create_task(
                self.dao1.get_messages(current_messages_params)
            )
            
            get_previous_messages_task = asyncio.create_task(
                self.dao2.get_messages(previous_messages_params)
            )

            # Tasks to run
            tasks = [get_current_messages_task, get_previous_messages_task]
            
            # Add stopwords task if needed
            if (params.retrieve_stopwords):
                get_stopwords_task = asyncio.create_task(
                    self.dao3.get_additional_stopwords(id_platform)
                )
                tasks.append(get_stopwords_task)
                current_messages, previous_messages, db_stopwords = await asyncio.gather(*tasks, return_exceptions=True)
            else:
                current_messages, previous_messages = await asyncio.gather(*tasks, return_exceptions=True)
                db_stopwords = []
            
            # Check if no messages were found for current period
            if not current_messages:
                self.logger.info("No messages were found for the current period.")
                empty_response = {
                    "current": {
                        "terms_retrieved_list": []
                    },
                    "previous": {
                        "terms_retrieved_list": []
                    }
                }
                return empty_response
            
            # Process stopwords
            stopwords = self.extend_db_stopwords(db_stopwords)
            
            # Process messages for current period
            current_processed_messages = TextProcessing.process_messages(current_messages)
            current_bag_of_words = TextProcessing.transform_messages_to_bag_of_words_dictionary(current_processed_messages, stopwords)
            
            # Process messages for previous period (if any)
            previous_bag_of_words = {}
            if previous_messages:
                previous_processed_messages = TextProcessing.process_messages(previous_messages)
                previous_bag_of_words = TextProcessing.transform_messages_to_bag_of_words_dictionary(previous_processed_messages, stopwords)
            
            # Generate the comparative response
            response = TextProcessing.transform_comparative_word_cloud_response(
                current_bag_of_words,
                previous_bag_of_words,
                params.algorithm,
                params.top_terms_count
            )

            return response
        except Exception as e:
            self.logger.exception(f"Failed to execute BubbleWordHandler: {e}")
            return {"error": str(e)}

    async def get_bubble_word_by_id_lead_list(self, id_platform, id_user, params):
        try:
            # self.dao tasks will run concurrently
            self.dao1 = AppMessageDAO()
            self.dao2 = AppMessageDAO()
            id_lead_list = params.id_lead_list
            
            get_messages_task = asyncio.create_task(
                self.dao1.get_messages_by_id_lead_list(id_platform, id_lead_list)
            )

            get_stopwords_task = asyncio.create_task(
                self.dao2.get_additional_stopwords(id_platform)
            )

            messages, db_stopwords = await asyncio.gather(
                get_messages_task,
                get_stopwords_task,
                return_exceptions=True
            )
            
            if not messages:
                self.logger.info(f"no messages were found in the database.")
                empty_json_data = {
                    "algorithm": "bag_of_words",
                    "collection_terms_retrieved": 0,
                    "collection_algorithm_frequency": 0,
                    "terms_retrieved": 0,
                    "terms_algorithm_frequency": 0,
                    "terms_retrieved_list": []
                }
                return empty_json_data
            
            stopwords = self.extend_db_stopwords(db_stopwords)
            
            processed_messages = TextProcessing.process_messages(messages)
            
            bag_of_words_dictionary = TextProcessing.transform_messages_to_bag_of_words_dictionary(processed_messages, stopwords)

            response = TextProcessing.transform_word_cloud_response(
                bag_of_words_dictionary,
                'bag_of_words',
                20,
                []
            )

            return response
        except Exception as e:
            self.logger.exception(f"Failed to execute BubleWordHandler: {e}")
            return {"error": str(e)}
